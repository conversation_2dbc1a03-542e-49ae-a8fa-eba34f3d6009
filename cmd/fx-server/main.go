package main

import (
	"context"
	"flag"
	"log"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	"wnapi/internal/pkg/config/viperconfig"

	fxapp "wnapi/internal/fx"

	// Import modules to register them with the FX module registry
	_ "wnapi/modules/agent-ai"
	_ "wnapi/modules/auth"
	_ "wnapi/modules/blog"
	_ "wnapi/modules/cart"
	_ "wnapi/modules/hello"
	_ "wnapi/modules/integration/socket"

	_ "wnapi/modules/marketing"
	_ "wnapi/modules/media"
	_ "wnapi/modules/notification"
	_ "wnapi/modules/onboarding"
	_ "wnapi/modules/product"
	_ "wnapi/modules/rbac"
	_ "wnapi/modules/seo"
	_ "wnapi/modules/site"
	_ "wnapi/modules/tenant"

	"go.uber.org/fx"
)

var (
	configFlag = flag.String("config", ".env", "Path to .env file")
	debugFlag  = flag.Bool("debug", false, "Enable debug mode")
)

func main() {
	// Parse command line flags
	flag.Parse()

	log.Println("Starting FX-based application...")

	// Load configuration
	configPath := *configFlag
	if !strings.HasPrefix(configPath, "@") {
		configPath = "@" + configPath
	}

	cfg, err := viperconfig.NewConfigLoader().Load(configPath)
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Get enabled modules from config
	enabledModules := strings.Split(cfg.GetString("MODULES_ENABLED"), ",")
	for i := range enabledModules {
		enabledModules[i] = strings.TrimSpace(enabledModules[i])
	}

	// Create FX application with config and enabled modules
	fxApp := fxapp.NewAppWithModules(enabledModules,
		// Supply the loaded config
		fx.Supply(cfg),
	)

	// Create context for graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Handle shutdown signals
	go func() {
		quit := make(chan os.Signal, 1)
		signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
		<-quit
		log.Println("Received shutdown signal...")
		cancel()
	}()

	// Start the application
	if err := fxApp.Start(ctx); err != nil {
		log.Fatalf("Failed to start application: %v", err)
	}

	// Wait for shutdown signal
	<-ctx.Done()

	// Stop the application with timeout
	stopCtx, stopCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer stopCancel()

	if err := fxApp.Stop(stopCtx); err != nil {
		log.Printf("Error during shutdown: %v", err)
	}

	log.Println("Application shutdown complete")
}
