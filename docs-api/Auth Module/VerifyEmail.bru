meta {
  name: Verify Email
  type: http
  seq: 8
}

post {
  url: {{api_url}}/api/admin/v1/auth/verify-email
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}

body:json {
  {
    "token": "verification_token_from_email" // Mã xác thực nhận được qua email (required)
  }
}

script:post-response {
  const data = res.getBody(); // Lấy phần data của response
  if (!data.verified) {
    throw new Error(`Verify email failed: ${data.message}`);
  }
  // Lưu JWT Token và thông tin user
  bru.setEnvVar("access_token", data.access_token);
  bru.setEnvVar("refresh_token", data.refresh_token);
  bru.setEnvVar("token_type", data.token_type);
  bru.setEnvVar("verified_email", data.verified);
}

docs {
  # Verify Email

  X<PERSON><PERSON> thực địa chỉ email người dùng bằng token nhận đượ<PERSON> qua email.

  ## Request Body
  ```json
  {
    "token": "string" // Mã xác thực (required)
  }
  ```

  ## Response Format
  ```json
  {
    "email": "<EMAIL>",
    "verified": true,
    "message": "Email verified successfully",
    "access_token": "string",
    "access_token_expires_in": 3600,
    "refresh_token": "string",
    "refresh_token_expires_in": 86400,
    "token_type": "Bearer",
    "user_id": 123,
    "status": "active",
    "onboarding_status": "completed",
    "tenant_id": 1
  }
  ```

  ## Error Responses
  - 400: Bad Request - Định dạng yêu cầu không hợp lệ
  - 404: Not Found - Token không hợp lệ hoặc đã hết hạn
  - 500: Internal Server Error - Lỗi máy chủ khi xác thực email
}

tests {
  test("Status code is 200", function() {
    expect(res.getStatus()).to.equal(200);
  });

  test("Email verified and tokens returned", function() {
    const data = res.getBody();
    expect(data.verified).to.be.true;
    expect(data.access_token).to.be.a("string");
    expect(data.refresh_token).to.be.a("string");
  });
}
