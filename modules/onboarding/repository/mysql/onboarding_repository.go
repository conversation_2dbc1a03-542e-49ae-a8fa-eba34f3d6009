package mysql

import (
	"context"
	"wnapi/modules/onboarding/models"
	"wnapi/modules/onboarding/repository"

	"gorm.io/gorm"
)

// onboardingRepository MySQL implementation cho onboarding repository
type onboardingRepository struct {
	db *gorm.DB
}

// NewOnboardingRepository tạo instance mới của onboarding repository
func NewOnboardingRepository(db *gorm.DB) repository.Repository {
	return &onboardingRepository{db: db}
}

// CreateOnboardingProgress tạo mới onboarding progress
func (r *onboardingRepository) CreateOnboardingProgress(ctx context.Context, progress *models.UserOnboardingProgress) error {
	return r.db.WithContext(ctx).Create(progress).Error
}

// GetOnboardingProgressByUser lấy tất cả onboarding progress của user
func (r *onboardingRepository) GetOnboardingProgressByUser(ctx context.Context, userID uint, tenantID, websiteID string) ([]*models.UserOnboardingProgress, error) {
	var progresses []*models.UserOnboardingProgress
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND tenant_id = ? AND website_id = ?", userID, tenantID, websiteID).
		Order("created_at ASC").
		Find(&progresses).Error
	if err != nil {
		return nil, err
	}
	return progresses, nil
}

// GetOnboardingProgressByUserAndStep lấy onboarding progress theo user và step
func (r *onboardingRepository) GetOnboardingProgressByUserAndStep(ctx context.Context, userID uint, tenantID, websiteID, stepName string) (*models.UserOnboardingProgress, error) {
	var progress models.UserOnboardingProgress
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND tenant_id = ? AND website_id = ? AND step_name = ?", userID, tenantID, websiteID, stepName).
		First(&progress).Error
	if err != nil {
		return nil, err
	}
	return &progress, nil
}

// UpdateOnboardingProgress cập nhật onboarding progress
func (r *onboardingRepository) UpdateOnboardingProgress(ctx context.Context, progress *models.UserOnboardingProgress) error {
	return r.db.WithContext(ctx).Save(progress).Error
}

// DeleteOnboardingProgress xóa onboarding progress
func (r *onboardingRepository) DeleteOnboardingProgress(ctx context.Context, userID uint, tenantID, websiteID, stepName string) error {
	return r.db.WithContext(ctx).
		Where("user_id = ? AND tenant_id = ? AND website_id = ? AND step_name = ?", userID, tenantID, websiteID, stepName).
		Delete(&models.UserOnboardingProgress{}).Error
}

// UpsertOnboardingProgress tạo mới hoặc cập nhật onboarding progress
func (r *onboardingRepository) UpsertOnboardingProgress(ctx context.Context, progress *models.UserOnboardingProgress) error {
	return r.db.WithContext(ctx).
		Where("user_id = ? AND tenant_id = ? AND website_id = ? AND step_name = ?", 
			progress.UserID, progress.TenantID, progress.WebsiteID, progress.StepName).
		Assign(progress).
		FirstOrCreate(progress).Error
}
