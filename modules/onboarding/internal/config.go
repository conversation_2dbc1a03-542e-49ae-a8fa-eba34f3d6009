package internal

// OnboardingConfig cấu hình cho module onboarding
type OnboardingConfig struct {
	// MaxStepsPerUser số bước tối đa cho mỗi user
	MaxStepsPerUser int `json:"max_steps_per_user" yaml:"max_steps_per_user"`
	
	// DefaultTenantID tenant ID mặc định khi không có tenant context
	DefaultTenantID string `json:"default_tenant_id" yaml:"default_tenant_id"`
	
	// DefaultWebsiteID website ID mặc định khi không có website context
	DefaultWebsiteID string `json:"default_website_id" yaml:"default_website_id"`
	
	// EnableTenantNameSuggestions bật/tắt tính năng gợi ý tên tenant
	EnableTenantNameSuggestions bool `json:"enable_tenant_name_suggestions" yaml:"enable_tenant_name_suggestions"`
	
	// EnableWebsiteTemplates bật/tắt tính năng mẫu website
	EnableWebsiteTemplates bool `json:"enable_website_templates" yaml:"enable_website_templates"`
}

// NewOnboardingConfig tạo cấu hình mặc định cho onboarding
func NewOnboardingConfig() *OnboardingConfig {
	return &OnboardingConfig{
		MaxStepsPerUser:             10,
		DefaultTenantID:             "default",
		DefaultWebsiteID:            "default",
		EnableTenantNameSuggestions: true,
		EnableWebsiteTemplates:      true,
	}
}
