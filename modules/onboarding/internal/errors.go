package internal

import "errors"

// Onboarding module specific errors
var (
	// ErrOnboardingAlreadyInitialized onboarding đã được khởi tạo
	ErrOnboardingAlreadyInitialized = errors.New("onboarding progress has already been initialized for this user")
	
	// ErrOnboardingNotInitialized onboarding chưa được khởi tạo
	ErrOnboardingNotInitialized = errors.New("onboarding progress not found. Please initialize first")
	
	// ErrStepAlreadyCompleted bước đã hoàn thành
	ErrStepAlreadyCompleted = errors.New("step has already been completed")
	
	// ErrStepCannotBeSkipped bước không thể bỏ qua
	ErrStepCannotBeSkipped = errors.New("step is mandatory and cannot be skipped")
	
	// ErrInvalidStepName tên bước không hợp lệ
	ErrInvalidStepName = errors.New("invalid step name")
	
	// ErrStepCompletionFailed không thể hoàn thành bước
	ErrStepCompletionFailed = errors.New("failed to process step completion")
	
	// ErrInvalidUserContext user context không hợp lệ
	ErrInvalidUserContext = errors.New("invalid user context")
	
	// ErrInvalidTenantContext tenant context không hợp lệ
	ErrInvalidTenantContext = errors.New("invalid tenant context")
	
	// ErrInvalidWebsiteContext website context không hợp lệ
	ErrInvalidWebsiteContext = errors.New("invalid website context")
)
