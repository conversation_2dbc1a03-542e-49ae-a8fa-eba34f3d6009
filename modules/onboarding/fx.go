package onboarding

import (
	"go.uber.org/fx"

	"wnapi/internal/fx/modules"
	"wnapi/modules/onboarding/api"
	"wnapi/modules/onboarding/repository/mysql"
	"wnapi/modules/onboarding/service"
)

// OnboardingModule implements FXModule interface
type OnboardingModule struct{}

// Name returns module name
func (m *OnboardingModule) Name() string {
	return "onboarding"
}

// Dependencies returns module dependencies
func (m *OnboardingModule) Dependencies() []string {
	return []string{"auth", "tenant"} // Depends on auth for JWT and tenant for multi-tenancy
}

// Priority returns loading priority
func (m *OnboardingModule) Priority() int {
	return 20 // Load after auth and tenant modules
}

// Enabled checks if module should be loaded
func (m *OnboardingModule) Enabled(config map[string]interface{}) bool {
	if enabled, ok := config["enabled"].(bool); ok {
		return enabled
	}
	return true // Default enabled
}

// GetMigrationPath returns path to module migrations
func (m *OnboardingModule) GetMigrationPath() string {
	return "modules/onboarding/migrations"
}

// GetMigrationOrder returns migration priority order
func (m *OnboardingModule) GetMigrationOrder() int {
	return 20 // Run after auth module migrations
}

// Module returns fx.Module for onboarding
func (m *OnboardingModule) Module() fx.Option {
	return fx.Module("onboarding",
		// Providers
		fx.Provide(
			// Repositories
			mysql.NewOnboardingRepository,

			// Services
			fx.Annotate(
				service.NewOnboardingService,
				fx.As(new(service.OnboardingService)),
			),

			// Handlers
			api.NewHandlerWithDependencies,
		),

		// Route registration using FX handler
		fx.Invoke(RegisterOnboardingRoutes),
	)
}

// RegisterOnboardingRoutes registers onboarding routes with the router
func RegisterOnboardingRoutes(handler *api.Handler, router interface{}) {
	if handler != nil {
		handler.RegisterRoutesWithServer(router)
	}
}

// Register onboarding module with global registry
func init() {
	modules.RegisterModule(&OnboardingModule{})
}
