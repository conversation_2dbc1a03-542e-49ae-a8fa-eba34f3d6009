-- Add tenant_id and website_id columns to user_onboarding_progress table
ALTER TABLE user_onboarding_progress 
ADD COLUMN tenant_id VARCHAR(255) NOT NULL DEFAULT 'default' AFTER user_id,
ADD COLUMN website_id VARCHAR(255) NOT NULL DEFAULT 'default' AFTER tenant_id;

-- Add indexes for the new columns
ALTER TABLE user_onboarding_progress 
ADD INDEX idx_user_onboarding_progress_tenant_id (tenant_id),
ADD INDEX idx_user_onboarding_progress_website_id (website_id),
ADD INDEX idx_user_onboarding_progress_tenant_website (user_id, tenant_id, website_id);

-- Update the unique constraint to include tenant_id and website_id
ALTER TABLE user_onboarding_progress 
DROP INDEX uk_user_step,
ADD UNIQUE KEY uk_user_tenant_website_step (user_id, tenant_id, website_id, step_name);
