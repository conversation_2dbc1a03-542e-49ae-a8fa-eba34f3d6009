package mysql

import (
	"context"
	"errors"
	"fmt"
	"time"

	"wnapi/internal/database"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/auth/internal"
	"wnapi/modules/auth/models"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

// mysqlRepository triển khai Repository interface sử dụng GORM
type mysqlRepository struct {
	db       *gorm.DB
	logger   logger.Logger
	userRepo *UserRepository
}

// NewMySQLRepository tạo một repository mới
func NewMySQLRepository(dbManager *database.Manager, logger logger.Logger) (internal.Repository, error) {
	if dbManager == nil {
		return nil, errors.New("database manager không được để trống")
	}

	sqlxDB := dbManager.GetDB()
	if sqlxDB == nil {
		return nil, errors.New("không thể kết nối tới cơ sở dữ liệu")
	}

	// Create GORM DB from sqlx DB
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn: sqlxDB.DB,
	}), &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("failed to initialize GORM: %w", err)
	}

	userRepo := NewUserRepository(gormDB, logger)

	return &mysqlRepository{
		db:       gormDB,
		logger:   logger,
		userRepo: userRepo,
	}, nil
}

// NewMySQLRepositoryWithDB tạo một repository mới từ gorm.DB
func NewMySQLRepositoryWithDB(db *gorm.DB, logger logger.Logger) (internal.Repository, error) {
	if db == nil {
		return nil, errors.New("gorm DB không được để trống")
	}

	userRepo := NewUserRepository(db, logger)

	return &mysqlRepository{
		db:       db,
		logger:   logger,
		userRepo: userRepo,
	}, nil
}

// CreateUser delegates to UserRepository
func (r *mysqlRepository) CreateUser(ctx context.Context, user *internal.User, password string) (*internal.User, error) {
	return r.userRepo.CreateUser(ctx, user, password)
}

// GetUserByID delegates to UserRepository
func (r *mysqlRepository) GetUserByID(ctx context.Context, id int) (*internal.User, error) {
	return r.userRepo.GetUserByID(ctx, id)
}

// GetUserByUsername delegates to UserRepository
func (r *mysqlRepository) GetUserByUsername(ctx context.Context, username string) (*internal.User, error) {
	return r.userRepo.GetUserByUsername(ctx, username)
}

// GetUserByEmail delegates to UserRepository
func (r *mysqlRepository) GetUserByEmail(ctx context.Context, email string) (*internal.User, error) {
	return r.userRepo.GetUserByEmail(ctx, email)
}

// UpdateUser delegates to UserRepository
func (r *mysqlRepository) UpdateUser(ctx context.Context, user *internal.User) error {
	return r.userRepo.UpdateUser(ctx, user)
}

// DeleteUser delegates to UserRepository
func (r *mysqlRepository) DeleteUser(ctx context.Context, id int) error {
	return r.userRepo.DeleteUser(ctx, id)
}

// CreateToken tạo token mới (sử dụng auth_sessions table)
func (r *mysqlRepository) CreateToken(ctx context.Context, token *internal.Token) error {
	// Tạo span cho truy vấn database
	ctx, span := tracing.DatabaseMiddleware(ctx, "INSERT", "auth_sessions")
	defer span.End()

	// Thêm thông tin về operation
	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "auth_sessions"),
		attribute.String("db.operation", "create_token"),
		attribute.Int("auth.user_id", token.UserID),
		attribute.String("auth.token_type", string(token.TokenType)),
	)

	// Nếu ID token chưa được thiết lập, tạo ID mới
	if token.ID == "" {
		token.ID = uuid.New().String()
	}

	token.CreatedAt = time.Now()
	token.UpdatedAt = time.Now()

	// Sử dụng GORM để tạo token
	sessionModel := models.AuthSession{
		ID:           token.ID,
		UserID:       uint(token.UserID),
		AccessToken:  token.AccessToken,
		RefreshToken: token.RefreshToken,
		ExpiresAt:    token.ExpiresAt,
		CreatedAt:    token.CreatedAt,
		UpdatedAt:    token.UpdatedAt,
	}

	tx := r.db.WithContext(ctx).Create(&sessionModel)

	if tx.Error != nil {
		r.logger.Error("Không thể tạo token", logger.String("error", tx.Error.Error()))
		tracing.RecordError(ctx, tx.Error)
		return tx.Error
	}

	// Che giấu thông tin nhạy cảm trong span
	if len(token.RefreshToken) > 8 {
		maskedToken := token.RefreshToken[0:8] + "..."
		tracing.AddSpanAttributes(ctx, attribute.String("auth.token_id", token.ID))
		tracing.AddSpanAttributes(ctx, attribute.String("auth.token_value_masked", maskedToken))
	}

	return nil
}

// GetTokenByValue lấy token theo giá trị và loại (sử dụng auth_sessions table)
func (r *mysqlRepository) GetTokenByValue(ctx context.Context, tokenValue string, tokenType internal.TokenType) (*internal.Token, error) {
	// Tạo span cho truy vấn database
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "auth_sessions")
	defer span.End()

	// Thêm thông tin về operation
	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "auth_sessions"),
		attribute.String("db.operation", "get_token_by_value"),
		attribute.String("auth.token_type", string(tokenType)),
	)

	// Che giấu thông tin nhạy cảm trong span
	if len(tokenValue) > 8 {
		maskedToken := tokenValue[0:8] + "..."
		tracing.AddSpanAttributes(ctx, attribute.String("auth.token_value_masked", maskedToken))
	}

	var sessionModel models.AuthSession
	tx := r.db.WithContext(ctx).Where("access_token = ? OR refresh_token = ?", tokenValue, tokenValue).First(&sessionModel)

	if tx.Error != nil {
		if errors.Is(tx.Error, gorm.ErrRecordNotFound) {
			tracing.AddSpanAttributes(ctx, attribute.Bool("db.record_found", false))
			return nil, internal.ErrTokenNotFound
		}
		r.logger.Error("Không thể lấy token", logger.String("error", tx.Error.Error()))
		tracing.RecordError(ctx, tx.Error)
		return nil, tx.Error
	}

	// Chuyển đổi model thành internal.Token
	token := &internal.Token{
		ID:           sessionModel.ID,
		UserID:       int(sessionModel.UserID),
		TokenType:    tokenType,
		AccessToken:  sessionModel.AccessToken,
		RefreshToken: sessionModel.RefreshToken,
		ExpiresAt:    sessionModel.ExpiresAt,
		CreatedAt:    sessionModel.CreatedAt,
		UpdatedAt:    sessionModel.UpdatedAt,
	}

	tracing.AddSpanAttributes(ctx,
		attribute.Bool("db.record_found", true),
		attribute.Int("auth.user_id", token.UserID),
		attribute.String("auth.token_id", token.ID),
		attribute.Bool("auth.token_expired", token.IsExpired()),
	)

	return token, nil
}

// DeleteToken xóa token (sử dụng auth_sessions table)
func (r *mysqlRepository) DeleteToken(ctx context.Context, id string) error {
	// Tạo span cho truy vấn database
	ctx, span := tracing.DatabaseMiddleware(ctx, "DELETE", "auth_sessions")
	defer span.End()

	// Thêm thông tin về operation
	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "auth_sessions"),
		attribute.String("db.operation", "delete_token"),
		attribute.String("auth.token_id", id),
	)

	tx := r.db.WithContext(ctx).Delete(&models.AuthSession{}, "id = ?", id)

	if tx.Error != nil {
		r.logger.Error("Không thể xóa token", logger.String("error", tx.Error.Error()))
		tracing.RecordError(ctx, tx.Error)
		return tx.Error
	}

	return nil
}

// DeleteExpiredTokens xóa tất cả token đã hết hạn (sử dụng auth_sessions table)
func (r *mysqlRepository) DeleteExpiredTokens(ctx context.Context) error {
	// Tạo span cho truy vấn database
	ctx, span := tracing.DatabaseMiddleware(ctx, "DELETE", "auth_sessions")
	defer span.End()

	// Thêm thông tin về operation
	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "auth_sessions"),
		attribute.String("db.operation", "delete_expired_tokens"),
	)

	// Sử dụng raw query vì đây là thao tác list/batch
	query := "DELETE FROM auth_sessions WHERE expires_at < ?"
	tracing.AddSpanAttributes(ctx, tracing.DBStatement(query))

	tx := r.db.WithContext(ctx).Exec(query, time.Now())
	if tx.Error != nil {
		r.logger.Error("Không thể xóa token hết hạn", logger.String("error", tx.Error.Error()))
		tracing.RecordError(ctx, tx.Error)
		return tx.Error
	}

	// Thêm thông tin về số lượng token đã xóa
	tracing.AddSpanAttributes(ctx, attribute.Int64("db.rows_affected", tx.RowsAffected))

	return nil
}

// GetTokensByUserID lấy tất cả token của một user (sử dụng auth_sessions table)
func (r *mysqlRepository) GetTokensByUserID(ctx context.Context, userID int) ([]*internal.Token, error) {
	// Tạo span cho truy vấn database
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "auth_sessions")
	defer span.End()

	// Thêm thông tin về operation
	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "auth_sessions"),
		attribute.String("db.operation", "get_tokens_by_user_id"),
		attribute.Int("auth.user_id", userID),
	)

	var sessionModels []models.AuthSession
	tx := r.db.WithContext(ctx).Where("user_id = ?", userID).Order("created_at DESC").Find(&sessionModels)

	if tx.Error != nil {
		r.logger.Error("Không thể lấy token của user", logger.String("error", tx.Error.Error()))
		tracing.RecordError(ctx, tx.Error)
		return nil, tx.Error
	}

	// Chuyển đổi models thành internal.Token
	tokens := make([]*internal.Token, len(sessionModels))
	for i, sessionModel := range sessionModels {
		tokens[i] = &internal.Token{
			ID:           sessionModel.ID,
			UserID:       int(sessionModel.UserID),
			TokenType:    internal.TokenTypeRefresh, // Giả định là refresh token
			AccessToken:  sessionModel.AccessToken,
			RefreshToken: sessionModel.RefreshToken,
			ExpiresAt:    sessionModel.ExpiresAt,
			CreatedAt:    sessionModel.CreatedAt,
			UpdatedAt:    sessionModel.UpdatedAt,
		}
	}

	tracing.AddSpanAttributes(ctx,
		attribute.Int("auth.tokens_count", len(tokens)),
	)

	return tokens, nil
}

// CountActiveTokensByUserID đếm số token chưa hết hạn của một user
func (r *mysqlRepository) CountActiveTokensByUserID(ctx context.Context, userID int) (int, error) {
	// Tạo span cho truy vấn database
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "auth_sessions")
	defer span.End()

	// Thêm thông tin về operation
	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "auth_sessions"),
		attribute.String("db.operation", "count_active_tokens_by_user_id"),
		attribute.Int("auth.user_id", userID),
	)

	var count int64
	tx := r.db.WithContext(ctx).Model(&models.AuthSession{}).
		Where("user_id = ? AND expires_at > ?", userID, time.Now()).
		Count(&count)

	if tx.Error != nil {
		r.logger.Error("Không thể đếm token của user", logger.String("error", tx.Error.Error()))
		tracing.RecordError(ctx, tx.Error)
		return 0, tx.Error
	}

	tracing.AddSpanAttributes(ctx,
		attribute.Int("auth.active_tokens_count", int(count)),
	)

	return int(count), nil
}

// DeleteOldestTokensByUserID xóa các token cũ nhất của user, chỉ giữ lại keepCount token mới nhất
func (r *mysqlRepository) DeleteOldestTokensByUserID(ctx context.Context, userID int, keepCount int) error {
	// Tạo span cho truy vấn database
	ctx, span := tracing.DatabaseMiddleware(ctx, "DELETE", "auth_sessions")
	defer span.End()

	// Thêm thông tin về operation
	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "auth_sessions"),
		attribute.String("db.operation", "delete_oldest_tokens_by_user_id"),
		attribute.Int("auth.user_id", userID),
		attribute.Int("auth.keep_count", keepCount),
	)

	// Sử dụng raw query để xóa token cũ nhất, chỉ giữ lại keepCount token mới nhất
	query := `
		DELETE FROM auth_sessions
		WHERE user_id = ? AND id NOT IN (
			SELECT id FROM (
				SELECT id FROM auth_sessions
				WHERE user_id = ?
				ORDER BY created_at DESC
				LIMIT ?
			) AS latest_sessions
		)
	`
	tracing.AddSpanAttributes(ctx, tracing.DBStatement(query))

	tx := r.db.WithContext(ctx).Exec(query, userID, userID, keepCount)
	if tx.Error != nil {
		r.logger.Error("Không thể xóa token cũ của user", logger.String("error", tx.Error.Error()))
		tracing.RecordError(ctx, tx.Error)
		return tx.Error
	}

	// Thêm thông tin về số lượng token đã xóa
	tracing.AddSpanAttributes(ctx, attribute.Int64("db.rows_affected", tx.RowsAffected))

	r.logger.Info("Đã xóa token cũ của user",
		logger.Int("user_id", userID),
		logger.Int("keep_count", keepCount),
		logger.Int("deleted_count", int(tx.RowsAffected)))

	return nil
}

// GetUserProfile delegates to UserRepository
func (r *mysqlRepository) GetUserProfile(ctx context.Context, userID int) (*models.Profile, error) {
	return r.userRepo.GetUserProfile(ctx, userID)
}

// UpdateUserProfile delegates to UserRepository
func (r *mysqlRepository) UpdateUserProfile(ctx context.Context, userID int, profile *models.Profile) error {
	return r.userRepo.UpdateUserProfile(ctx, userID, profile)
}

// ListUsers delegates to UserRepository
func (r *mysqlRepository) ListUsers(ctx context.Context, tenantID uint, cursor string, limit int, filters map[string]interface{}) ([]*internal.User, string, error) {
	return r.userRepo.ListUsers(ctx, tenantID, cursor, limit, filters)
}

// CreateUserTenant creates a user-tenant relationship
func (r *mysqlRepository) CreateUserTenant(ctx context.Context, userID, tenantID, roleID uint) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "INSERT", "user_tenants")
	defer span.End()

	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "user_tenants"),
		attribute.String("db.operation", "create_user_tenant"),
		attribute.Int("user_id", int(userID)),
		attribute.Int("tenant_id", int(tenantID)),
		attribute.Int("role_id", int(roleID)),
	)

	userTenant := models.UserTenant{
		UserID:   userID,
		TenantID: tenantID,
		RoleID:   roleID,
		Status:   "active",
	}

	tx := r.db.WithContext(ctx).Create(&userTenant)
	if tx.Error != nil {
		r.logger.Error("Failed to create user-tenant relationship", logger.String("error", tx.Error.Error()))
		tracing.RecordError(ctx, tx.Error)
		return tx.Error
	}

	return nil
}

// GetUserTenants gets all tenants for a user
func (r *mysqlRepository) GetUserTenants(ctx context.Context, userID uint) ([]internal.UserTenantInfo, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "user_tenants")
	defer span.End()

	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "user_tenants"),
		attribute.String("db.operation", "get_user_tenants"),
		attribute.Int("user_id", int(userID)),
	)

	// Thực hiện JOIN với bảng tenants và rbac_roles để lấy thông tin tenant_name và role_name
	var userTenantInfos []internal.UserTenantInfo
	query := `
		SELECT 
			ut.user_id, 
			ut.tenant_id, 
			ut.role_id, 
			ut.status, 
			ut.created_at, 
			ut.updated_at,
			t.tenant_name,
			r.role_name
		FROM 
			user_tenants ut
		LEFT JOIN 
			tenants t ON ut.tenant_id = t.tenant_id
		LEFT JOIN 
			rbac_roles r ON ut.role_id = r.role_id
		WHERE 
			ut.user_id = ? AND ut.status = ?
	`

	tx := r.db.WithContext(ctx).Raw(query, userID, "active").Scan(&userTenantInfos)
	if tx.Error != nil {
		r.logger.Error("Failed to get user tenants", logger.String("error", tx.Error.Error()))
		tracing.RecordError(ctx, tx.Error)
		return nil, tx.Error
	}

	return userTenantInfos, nil
}

// GetTenantUsers gets all users for a tenant
func (r *mysqlRepository) GetTenantUsers(ctx context.Context, tenantID uint) ([]internal.UserTenantInfo, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "user_tenants")
	defer span.End()

	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "user_tenants"),
		attribute.String("db.operation", "get_tenant_users"),
		attribute.Int("tenant_id", int(tenantID)),
	)

	var userTenants []models.UserTenant
	tx := r.db.WithContext(ctx).Where("tenant_id = ? AND status = ?", tenantID, "active").Find(&userTenants)
	if tx.Error != nil {
		r.logger.Error("Failed to get tenant users", logger.String("error", tx.Error.Error()))
		tracing.RecordError(ctx, tx.Error)
		return nil, tx.Error
	}

	// Convert to internal type
	result := make([]internal.UserTenantInfo, len(userTenants))
	for i, ut := range userTenants {
		result[i] = internal.UserTenantInfo{
			UserID:    ut.UserID,
			TenantID:  ut.TenantID,
			RoleID:    ut.RoleID,
			Status:    ut.Status,
			CreatedAt: ut.CreatedAt,
			UpdatedAt: ut.UpdatedAt,
		}
	}

	return result, nil
}

// UpdateUserTenantRole updates the role for a user-tenant relationship
func (r *mysqlRepository) UpdateUserTenantRole(ctx context.Context, userID, tenantID, roleID uint) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "UPDATE", "user_tenants")
	defer span.End()

	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "user_tenants"),
		attribute.String("db.operation", "update_user_tenant_role"),
		attribute.Int("user_id", int(userID)),
		attribute.Int("tenant_id", int(tenantID)),
		attribute.Int("role_id", int(roleID)),
	)

	tx := r.db.WithContext(ctx).Model(&models.UserTenant{}).
		Where("user_id = ? AND tenant_id = ?", userID, tenantID).
		Update("role_id", roleID)

	if tx.Error != nil {
		r.logger.Error("Failed to update user-tenant role", logger.String("error", tx.Error.Error()))
		tracing.RecordError(ctx, tx.Error)
		return tx.Error
	}

	return nil
}

// DeleteUserTenant removes a user-tenant relationship
func (r *mysqlRepository) DeleteUserTenant(ctx context.Context, userID, tenantID uint) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "DELETE", "user_tenants")
	defer span.End()

	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "user_tenants"),
		attribute.String("db.operation", "delete_user_tenant"),
		attribute.Int("user_id", int(userID)),
		attribute.Int("tenant_id", int(tenantID)),
	)

	tx := r.db.WithContext(ctx).Where("user_id = ? AND tenant_id = ?", userID, tenantID).Delete(&models.UserTenant{})
	if tx.Error != nil {
		r.logger.Error("Failed to delete user-tenant relationship", logger.String("error", tx.Error.Error()))
		tracing.RecordError(ctx, tx.Error)
		return tx.Error
	}

	return nil
}
