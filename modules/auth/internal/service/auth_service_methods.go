package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/auth/dto"
	"wnapi/modules/auth/internal"
	"wnapi/modules/auth/models"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"golang.org/x/crypto/bcrypt"
)

// ChangePassword thay đổi mật khẩu của người dùng
func (s *authServiceImpl) ChangePassword(ctx context.Context, userID uint, req dto.ChangePasswordRequest) (*dto.ChangePasswordResponse, error) {
	// Tạo span cho toàn bộ quá trình thay đổi mật khẩu
	ctx, span := tracing.StartSpan(ctx, "auth-service", "change_password")
	defer span.End()

	// Thêm thông tin vào span
	tracing.AddSpanAttributes(ctx, attribute.Int("auth.user_id", int(userID)))

	// L<PERSON>y thông tin người dùng từ database
	var user *internal.User
	err := tracing.WithSpan(ctx, "auth-service", "get_user", func(ctx context.Context) error {
		var err error
		user, err = s.repo.GetUserByID(ctx, int(userID))
		if err != nil {
			if errors.Is(err, internal.ErrUserNotFound) {
				tracing.AddSpanAttributes(ctx, attribute.String("auth.error", "user_not_found"))
				return internal.ErrUserNotFound
			}
			s.logger.Error("Failed to get user", logger.String("error", err.Error()))
			return err
		}
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Kiểm tra mật khẩu hiện tại
	err = tracing.WithSpan(ctx, "auth-service", "verify_current_password", func(ctx context.Context) error {
		if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.CurrentPassword)); err != nil {
			s.logger.Warn("Invalid current password", logger.Int("user_id", int(userID)))
			tracing.AddSpanAttributes(ctx, attribute.String("auth.error", "invalid_password"))
			return internal.ErrInvalidPassword
		}
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Hash mật khẩu mới
	var hashedPassword string
	err = tracing.WithSpan(ctx, "auth-service", "hash_new_password", func(ctx context.Context) error {
		hash, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
		if err != nil {
			s.logger.Error("Failed to hash new password", logger.String("error", err.Error()))
			return err
		}
		hashedPassword = string(hash)
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Cập nhật mật khẩu trong database
	err = tracing.WithSpan(ctx, "auth-service", "update_password", func(ctx context.Context) error {
		user.PasswordHash = hashedPassword
		if err := s.repo.UpdateUser(ctx, user); err != nil {
			s.logger.Error("Failed to update password", logger.String("error", err.Error()))
			return err
		}
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Đánh dấu thành công trong span
	tracing.SetSpanStatus(ctx, codes.Ok, "Password changed successfully")
	tracing.AddSpanAttributes(ctx, attribute.Bool("auth.success", true))

	return &dto.ChangePasswordResponse{
		Message: "Password changed successfully",
		Success: true,
	}, nil
}

// GetProfile lấy thông tin profile của người dùng
func (s *authServiceImpl) GetProfile(ctx context.Context, userID uint) (*dto.ProfileResponse, error) {
	ctx, span := tracing.StartSpan(ctx, "auth-service", "get_profile")
	defer span.End()

	tracing.AddSpanAttributes(ctx, attribute.Int("auth.user_id", int(userID)))

	// Lấy thông tin user
	var user *internal.User
	err := tracing.WithSpan(ctx, "auth-service", "get_user", func(ctx context.Context) error {
		var err error
		user, err = s.repo.GetUserByID(ctx, int(userID))
		if err != nil {
			if errors.Is(err, internal.ErrUserNotFound) {
				return internal.ErrUserNotFound
			}
			s.logger.Error("Failed to get user", logger.String("error", err.Error()))
			return err
		}
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Lấy thông tin profile
	var profile *models.Profile
	err = tracing.WithSpan(ctx, "auth-service", "get_profile_details", func(ctx context.Context) error {
		var err error
		profile, err = s.repo.GetUserProfile(ctx, int(userID))
		if err != nil {
			// Profile có thể không tồn tại, đây không phải lỗi nghiêm trọng
			s.logger.Warn("Profile not found for user", logger.Int("user_id", int(userID)))
			profile = nil
		}
		return nil
	})

	// Tạo response
	response := &dto.ProfileResponse{
		UserID:          user.UserID,
		Username:        user.Username,
		Email:           user.Email,
		FullName:        user.FullName,
		Status:          user.Status,
		IsEmailVerified: user.IsEmailVerified,
		UserType:        user.UserType,
		CreatedAt:       user.CreatedAt,
	}

	// Thêm thông tin profile nếu có
	if profile != nil {
		response.AvatarURL = profile.AvatarURL
		response.Phone = profile.Phone
		response.Address = profile.Address
		response.City = profile.City
		response.State = profile.State
		response.Country = profile.Country
		response.PostalCode = profile.PostalCode
		response.DateOfBirth = profile.DateOfBirth
		response.Gender = profile.Gender
		response.PreferredLanguage = profile.PreferredLanguage
		response.Timezone = profile.Timezone
		response.NotificationPreferences = profile.NotificationPreferences
		response.SocialLinkedin = profile.SocialLinkedin
		response.SocialTwitter = profile.SocialTwitter
		response.SocialFacebook = profile.SocialFacebook
		response.SocialInstagram = profile.SocialInstagram
		response.Skills = profile.Skills
		response.Interests = profile.Interests
		response.ProfessionalTitle = profile.ProfessionalTitle
		response.Department = profile.Department
	}

	tracing.SetSpanStatus(ctx, codes.Ok, "Profile retrieved successfully")
	return response, nil
}

// VerifyEmail xác thực email của người dùng
func (s *authServiceImpl) VerifyEmail(ctx context.Context, token string) (*dto.VerifyEmailResponse, error) {
	ctx, span := tracing.StartSpan(ctx, "auth-service", "verify_email")
	defer span.End()

	if s.emailVerificationRepo == nil {
		return nil, fmt.Errorf("email verification repository not available")
	}

	// Lấy verification record
	verification, err := s.emailVerificationRepo.GetByToken(ctx, token)
	if err != nil {
		s.logger.Error("Failed to get verification record", logger.String("error", err.Error()))
		return nil, internal.ErrInvalidToken
	}

	// Kiểm tra verification record có tồn tại không
	if verification == nil {
		s.logger.Error("Verification record not found", logger.String("token", token))
		return nil, internal.ErrInvalidToken
	}

	// Kiểm tra token có hết hạn không
	if time.Now().After(verification.ExpiresAt) {
		return nil, internal.ErrExpiredToken
	}

	// Lấy user và cập nhật trạng thái verified
	user, err := s.repo.GetUserByID(ctx, int(verification.UserID))
	if err != nil {
		s.logger.Error("Failed to get user for verification", logger.String("error", err.Error()))
		return nil, err
	}

	// Cập nhật user: set email verified và chuyển status từ email_verification_required sang active
	user.IsEmailVerified = true
	if user.Status == "email_verification_required" {
		user.Status = "active"
	}
	if err := s.repo.UpdateUser(ctx, user); err != nil {
		s.logger.Error("Failed to update user verification status", logger.String("error", err.Error()))
		return nil, err
	}

	// Set default onboarding status (vì internal.User không có field này)
	// TODO: Implement proper onboarding status retrieval from models.User
	onboardingStatus := "not_started"

	// Đánh dấu verification record đã được sử dụng
	if err := s.emailVerificationRepo.MarkAsVerified(ctx, token); err != nil {
		s.logger.Error("Failed to mark verification as used", logger.String("error", err.Error()))
		// Không return error vì user đã được verified thành công
	}

	// Tạo JWT tokens giống như trong Login
	var accessToken string
	err = tracing.WithSpan(ctx, "auth-service", "generate_tokens", func(ctx context.Context) error {
		var err error
		accessToken, _, err = s.generateJWTToken(int(user.UserID), user.Username, s.config.AccessTokenExpiry)
		if err != nil {
			s.logger.Error("Failed to generate access token", logger.String("error", err.Error()))
			return err
		}
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Tạo refresh token
	refreshToken := uuid.NewString()
	refreshExpiry := time.Now().Add(s.config.RefreshTokenExpiry)

	// Lưu refresh token vào database
	err = tracing.WithSpan(ctx, "auth-service", "save_refresh_token", func(ctx context.Context) error {
		token := &internal.Token{
			UserID:       int(user.UserID),
			TokenType:    internal.TokenTypeRefresh,
			AccessToken:  accessToken,
			RefreshToken: refreshToken,
			ExpiresAt:    refreshExpiry,
		}

		if err := s.repo.CreateToken(ctx, token); err != nil {
			s.logger.Error("Failed to save refresh token", logger.String("error", err.Error()))
			return err
		}
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	tracing.SetSpanStatus(ctx, codes.Ok, "Email verified successfully")
	return &dto.VerifyEmailResponse{
		// Email verification info
		Email:    user.Email,
		Verified: true,
		Message:  "Email verified successfully",

		// JWT tokens (same as LoginResponse)
		AccessToken:           accessToken,
		AccessTokenExpiresIn:  int(s.config.AccessTokenExpiry.Seconds()),
		RefreshToken:          refreshToken,
		RefreshTokenExpiresIn: int(s.config.RefreshTokenExpiry.Seconds()),
		TokenType:             "Bearer",

		// User information
		UserID:           int64(user.UserID),
		Status:           string(user.Status),
		OnboardingStatus: onboardingStatus,
		TenantID:         0, // TODO: Implement tenant selection in multi-tenant architecture
	}, nil
}

// ResendVerificationEmail gửi lại email xác thực
func (s *authServiceImpl) ResendVerificationEmail(ctx context.Context, email string) (*dto.ResendVerificationEmailResponse, error) {
	ctx, span := tracing.StartSpan(ctx, "auth-service", "resend_verification_email")
	defer span.End()

	// Lấy user theo email
	user, err := s.repo.GetUserByEmail(ctx, email)
	if err != nil {
		if errors.Is(err, internal.ErrUserNotFound) {
			return nil, internal.ErrUserNotFound
		}
		return nil, err
	}

	// Kiểm tra user đã verified chưa
	if user.IsEmailVerified {
		return &dto.ResendVerificationEmailResponse{
			Message: "Email already verified",
			Success: false,
		}, nil
	}

	// Gửi email verification
	if err := s.sendVerificationEmail(ctx, int(user.UserID), user.Email, user.FullName); err != nil {
		s.logger.Error("Failed to resend verification email", logger.String("error", err.Error()))
		return nil, err
	}

	return &dto.ResendVerificationEmailResponse{
		Message: "Verification email sent successfully",
		Success: true,
	}, nil
}

// ForgotPassword xử lý yêu cầu quên mật khẩu
func (s *authServiceImpl) ForgotPassword(ctx context.Context, req dto.ForgotPasswordRequest) (*dto.ForgotPasswordResponse, error) {
	// Implementation placeholder - this would typically involve:
	// 1. Find user by email
	// 2. Generate reset token
	// 3. Send reset email
	// 4. Store reset token in database
	return &dto.ForgotPasswordResponse{
		Message: "Password reset email sent if account exists",
		Success: true,
	}, nil
}

// ResetPassword xử lý reset mật khẩu
func (s *authServiceImpl) ResetPassword(ctx context.Context, req dto.ResetPasswordRequest) (*dto.ResetPasswordResponse, error) {
	// Implementation placeholder - this would typically involve:
	// 1. Validate reset token
	// 2. Update user password
	// 3. Invalidate reset token
	return &dto.ResetPasswordResponse{
		Message: "Password reset successfully",
		Success: true,
	}, nil
}

// VerifyResetToken xác thực token reset mật khẩu
func (s *authServiceImpl) VerifyResetToken(ctx context.Context, req dto.VerifyResetTokenRequest) (*dto.VerifyResetTokenResponse, error) {
	// Implementation placeholder - this would typically involve:
	// 1. Validate reset token
	// 2. Check expiration
	// 3. Return validation result
	return &dto.VerifyResetTokenResponse{
		Valid:   true,
		Message: "Reset token is valid",
	}, nil
}

// UpdateProfile cập nhật thông tin profile của người dùng
func (s *authServiceImpl) UpdateProfile(ctx context.Context, userID uint, req dto.UpdateProfileRequest) (*dto.UpdateProfileResponse, error) {
	ctx, span := tracing.StartSpan(ctx, "auth-service", "update_profile")
	defer span.End()

	tracing.AddSpanAttributes(ctx, attribute.Int("auth.user_id", int(userID)))

	// Lấy thông tin user hiện tại
	var user *internal.User
	err := tracing.WithSpan(ctx, "auth-service", "get_user", func(ctx context.Context) error {
		var err error
		user, err = s.repo.GetUserByID(ctx, int(userID))
		if err != nil {
			if errors.Is(err, internal.ErrUserNotFound) {
				return internal.ErrUserNotFound
			}
			s.logger.Error("Failed to get user", logger.String("error", err.Error()))
			return err
		}
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Lấy thông tin profile hiện tại hoặc tạo mới nếu chưa có
	var profile *models.Profile
	err = tracing.WithSpan(ctx, "auth-service", "get_profile_details", func(ctx context.Context) error {
		var err error
		profile, err = s.repo.GetUserProfile(ctx, int(userID))
		if err != nil {
			// Nếu profile chưa tồn tại, tạo mới
			if errors.Is(err, internal.ErrUserNotFound) {
				profile = &models.Profile{
					UserID: userID,
				}
			} else {
				s.logger.Error("Failed to get profile", logger.String("error", err.Error()))
				return err
			}
		}
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Cập nhật thông tin cơ bản của user nếu có
	if req.FullName != nil {
		user.FullName = *req.FullName
		err = s.repo.UpdateUser(ctx, user)
		if err != nil {
			s.logger.Error("Failed to update user", logger.String("error", err.Error()))
			tracing.RecordError(ctx, err)
			return nil, err
		}
	}

	// Cập nhật thông tin profile
	if req.AvatarURL != nil {
		profile.AvatarURL = *req.AvatarURL
	}
	if req.Phone != nil {
		profile.Phone = *req.Phone
	}
	if req.Address != nil {
		profile.Address = *req.Address
	}
	if req.City != nil {
		profile.City = *req.City
	}
	if req.State != nil {
		profile.State = *req.State
	}
	if req.Country != nil {
		profile.Country = *req.Country
	}
	if req.PostalCode != nil {
		profile.PostalCode = *req.PostalCode
	}
	if req.DateOfBirth != nil {
		profile.DateOfBirth = req.DateOfBirth
	}
	if req.Gender != nil {
		profile.Gender = *req.Gender
	}
	if req.PreferredLanguage != nil {
		profile.PreferredLanguage = *req.PreferredLanguage
	}
	if req.Timezone != nil {
		profile.Timezone = *req.Timezone
	}
	if req.NotificationPreferences != nil {
		profile.NotificationPreferences = *req.NotificationPreferences
	}
	if req.SocialLinkedin != nil {
		profile.SocialLinkedin = *req.SocialLinkedin
	}
	if req.SocialTwitter != nil {
		profile.SocialTwitter = *req.SocialTwitter
	}
	if req.SocialFacebook != nil {
		profile.SocialFacebook = *req.SocialFacebook
	}
	if req.SocialInstagram != nil {
		profile.SocialInstagram = *req.SocialInstagram
	}
	if req.Skills != nil {
		profile.Skills = *req.Skills
	}
	if req.Interests != nil {
		profile.Interests = *req.Interests
	}
	if req.ProfessionalTitle != nil {
		profile.ProfessionalTitle = *req.ProfessionalTitle
	}
	if req.Department != nil {
		profile.Department = *req.Department
	}

	// Lưu thông tin profile
	err = s.repo.UpdateUserProfile(ctx, int(userID), profile)
	if err != nil {
		s.logger.Error("Failed to update profile", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Chuyển đổi từ internal.User sang models.User
	modelUser := &models.User{
		UserID:          user.UserID,
		Username:        user.Username,
		Email:           user.Email,
		PasswordHash:    user.PasswordHash,
		FullName:        user.FullName,
		CreatedAt:       user.CreatedAt,
		UpdatedAt:       user.UpdatedAt,
		LastLogin:       user.LastLogin,
		Status:          models.UserStatus(user.Status),
		IsEmailVerified: user.IsEmailVerified,
		UserType:        models.ParseUserType(user.UserType),
	}

	// Lấy thông tin profile đã cập nhật
	response := dto.NewProfileResponse(modelUser, profile)

	tracing.SetSpanStatus(ctx, codes.Ok, "Profile updated successfully")
	return response, nil
}
