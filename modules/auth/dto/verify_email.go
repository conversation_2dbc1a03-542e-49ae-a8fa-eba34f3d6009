package dto

// VerifyEmailRequest chứa token để xác thực email
type VerifyEmailRequest struct {
	Token string `json:"token" form:"token" binding:"required"`
}

// ResendVerificationEmailRequest chứa email để gửi lại email xác thực
type ResendVerificationEmailRequest struct {
	Email string `json:"email" binding:"required,email"`
}

// VerifyEmailResponse chứa thông tin phản hồi cho việc xác thực email
// Bao gồm JWT tokens và thông tin user giống như LoginResponse
type VerifyEmailResponse struct {
	// Email verification info
	Email    string `json:"email"`
	Verified bool   `json:"verified"`
	Message  string `json:"message"`

	// JWT tokens (same as LoginResponse)
	AccessToken           string `json:"access_token"`
	AccessTokenExpiresIn  int    `json:"access_token_expires_in"`
	RefreshToken          string `json:"refresh_token"`
	RefreshTokenExpiresIn int    `json:"refresh_token_expires_in"`
	TokenType             string `json:"token_type"`

	// User information
	UserID           int64  `json:"user_id"`
	Status           string `json:"status"`
	OnboardingStatus string `json:"onboarding_status"`
	TenantID         int    `json:"tenant_id"`
}

// ResendVerificationEmailResponse chứa thông tin phản hồi cho việc gửi lại email xác thực
type ResendVerificationEmailResponse struct {
	Message string `json:"message"`
	Success bool   `json:"success"`
}
