# Claude CLI Plugin

A plugin for integrating Claude AI CLI into the application, providing AI-powered code assistance and chat functionality.

## Features

- **Command Execution**: Execute Claude CLI commands from within the application
- **Interactive Chat**: Interactive chat interface with <PERSON>
- **Code Review**: AI-powered code review functionality
- **Code Generation**: Generate code based on requirements
- **File Analysis**: Analyze files using Claude AI
- **HTTP API**: RESTful API endpoints for Claude integration
- **Configuration**: Environment-based configuration

## Installation

1. Ensure Claude CLI is installed and available in your PATH:
   ```bash
   which claude
   ```

2. Enable the plugin in your configuration:
   ```bash
   # In .env file
   PLUGINS_ENABLED=claude-cli
   ```

3. Configure plugin settings (optional):
   ```bash
   PLUGIN_CLAUDE_CLI_ENABLED=true
   PLUGIN_CLAUDE_CLI_TIMEOUT=30
   PLUGIN_CLAUDE_CLI_INTERACTIVE=false
   PLUGIN_CLAUDE_CLI_MAX_RETRIES=3
   PLUGIN_CLAUDE_CLI_RETRY_DELAY=1
   ```

## Configuration

The plugin supports the following environment variables:

| Variable | Default | Description |
|----------|---------|-------------|
| `PLUGIN_CLAUDE_CLI_ENABLED` | `true` | Enable/disable the plugin |
| `PLUGIN_CLAUDE_CLI_TIMEOUT` | `30` | Timeout for Claude requests (seconds) |
| `PLUGIN_CLAUDE_CLI_INTERACTIVE` | `false` | Enable interactive mode |
| `PLUGIN_CLAUDE_CLI_MAX_RETRIES` | `3` | Maximum retry attempts |
| `PLUGIN_CLAUDE_CLI_RETRY_DELAY` | `1` | Delay between retries (seconds) |

## API Endpoints

### Chat with Claude
```http
POST /api/plugins/claude/chat
Content-Type: application/json

{
  "prompt": "Hello, how are you?"
}
```

### Code Review
```http
POST /api/plugins/claude/code-review
Content-Type: application/json

{
  "code": "func main() { fmt.Println(\"Hello World\") }"
}
```

### Code Generation
```http
POST /api/plugins/claude/code-generation
Content-Type: application/json

{
  "requirements": "Create a function that calculates fibonacci numbers"
}
```

### File Analysis
```http
POST /api/plugins/claude/file-analysis
Content-Type: application/json

{
  "file_path": "/path/to/file.go"
}
```

### Plugin Status
```http
GET /api/plugins/claude/status
```

## Usage Examples

### Interactive Chat
```go
plugin := &ClaudePlugin{}
err := plugin.InteractiveChat()
if err != nil {
    log.Fatal(err)
}
```

### Send Single Prompt
```go
plugin := &ClaudePlugin{}
response, err := plugin.SendPrompt("Explain Go channels")
if err != nil {
    log.Fatal(err)
}
fmt.Println(response)
```

### Code Review
```go
plugin := &ClaudePlugin{}
code := `func add(a, b int) int { return a + b }`
review, err := plugin.CodeReview(code)
if err != nil {
    log.Fatal(err)
}
fmt.Println(review)
```

## CLI Commands

The plugin provides interactive commands when in chat mode:

- `exit` or `quit` - Exit the chat
- `help` - Show help message
- `status` - Show plugin status

## Error Handling

The plugin handles various error conditions:

- **Claude CLI not installed**: Checks for Claude CLI availability
- **Timeout errors**: Configurable timeout for long-running requests
- **Command failures**: Proper error propagation from Claude CLI
- **Invalid input**: Validation of input parameters

## Security Considerations

- The plugin executes external commands (Claude CLI)
- File access is controlled through the application's file system permissions
- No sensitive data is logged or exposed
- Timeout protection prevents hanging processes

## Development

### Plugin Structure
```
plugins/claude-cli/
├── plugin.go      # Main plugin implementation
├── config.go      # Configuration handling
├── handlers.go    # HTTP handlers
└── README.md      # Documentation
```

### Testing
```bash
# Run plugin tests
go test ./plugins/claude-cli/...

# Test with specific config
PLUGIN_CLAUDE_CLI_TIMEOUT=5 go test ./plugins/claude-cli/...
```

## Troubleshooting

### Claude CLI Not Found
```bash
# Check if Claude CLI is installed
which claude

# Install Claude CLI (if not installed)
# Follow Claude CLI installation instructions
```

### Permission Errors
```bash
# Ensure Claude CLI has proper permissions
chmod +x $(which claude)
```

### Timeout Issues
```bash
# Increase timeout in configuration
export PLUGIN_CLAUDE_CLI_TIMEOUT=60
```

## Contributing

1. Follow the existing code style and patterns
2. Add tests for new functionality
3. Update documentation as needed
4. Ensure compatibility with the plugin interface