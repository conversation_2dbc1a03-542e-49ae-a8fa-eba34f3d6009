package claudecli

import (
	"bufio"
	"context"
	"fmt"
	"os"
	"os/exec"
	"strings"
	"time"

	"wnapi/plugins"
)

type ClaudePlugin struct {
	name            string
	config          map[string]any
	enabled         bool
	timeout         time.Duration
	interactiveMode bool
}

func NewClaudePlugin(config map[string]any) (plugins.Plugin, error) {
	plugin := &ClaudePlugin{
		name:    "claude-cli",
		config:  config,
		enabled: true,
		timeout: 30 * time.Second,
	}

	if timeout, ok := config["timeout"].(int); ok {
		plugin.timeout = time.Duration(timeout) * time.Second
	}

	if interactive, ok := config["interactive"].(bool); ok {
		plugin.interactiveMode = interactive
	}

	return plugin, nil
}

func (p *ClaudePlugin) Name() string {
	return p.name
}

func (p *ClaudePlugin) Init(ctx context.Context) error {
	if !p.enabled {
		return nil
	}

	if err := p.checkClaudeInstalled(); err != nil {
		return fmt.Errorf("claude <PERSON> not available: %w", err)
	}

	return nil
}

func (p *ClaudePlugin) Shutdown(ctx context.Context) error {
	return nil
}

func (p *ClaudePlugin) checkClaudeInstalled() error {
	_, err := exec.LookPath("claude")
	if err != nil {
		return fmt.Errorf("claude CLI not found in PATH")
	}
	return nil
}

func (p *ClaudePlugin) SendPrompt(prompt string) (string, error) {
	if !p.enabled {
		return "", fmt.Errorf("claude plugin is disabled")
	}

	ctx, cancel := context.WithTimeout(context.Background(), p.timeout)
	defer cancel()

	cmd := exec.CommandContext(ctx, "claude", prompt)
	output, err := cmd.Output()
	if err != nil {
		if ctx.Err() == context.DeadlineExceeded {
			return "", fmt.Errorf("claude request timed out after %v", p.timeout)
		}
		return "", fmt.Errorf("claude command failed: %w", err)
	}

	return strings.TrimSpace(string(output)), nil
}

func (p *ClaudePlugin) InteractiveChat() error {
	if !p.enabled {
		return fmt.Errorf("claude plugin is disabled")
	}

	scanner := bufio.NewScanner(os.Stdin)
	fmt.Println("Chat with Claude (type 'exit' to quit, 'help' for commands):")

	for {
		fmt.Print("> ")
		if !scanner.Scan() {
			break
		}

		input := strings.TrimSpace(scanner.Text())
		if input == "" {
			continue
		}

		switch input {
		case "exit", "quit":
			fmt.Println("Goodbye!")
			return nil
		case "help":
			p.printHelp()
			continue
		case "status":
			p.printStatus()
			continue
		}

		response, err := p.SendPrompt(input)
		if err != nil {
			fmt.Printf("Error: %v\n", err)
			continue
		}

		fmt.Printf("Claude: %s\n\n", response)
	}

	return scanner.Err()
}

func (p *ClaudePlugin) printHelp() {
	fmt.Print(`
Available commands:
  exit, quit - Exit the chat
  help       - Show this help message
  status     - Show plugin status
  
Just type your message to chat with Claude.
`)
}

func (p *ClaudePlugin) printStatus() {
	fmt.Printf(`
Claude Plugin Status:
  Enabled: %v
  Timeout: %v
  Interactive Mode: %v
`, p.enabled, p.timeout, p.interactiveMode)
}

func (p *ClaudePlugin) ProcessFile(filePath string) (string, error) {
	if !p.enabled {
		return "", fmt.Errorf("claude plugin is disabled")
	}

	content, err := os.ReadFile(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to read file: %w", err)
	}

	prompt := fmt.Sprintf("Please analyze this file:\n\n%s", string(content))
	return p.SendPrompt(prompt)
}

func (p *ClaudePlugin) CodeReview(code string) (string, error) {
	if !p.enabled {
		return "", fmt.Errorf("claude plugin is disabled")
	}

	prompt := fmt.Sprintf("Please review this code and provide feedback:\n\n%s", code)
	return p.SendPrompt(prompt)
}

func (p *ClaudePlugin) GenerateCode(requirements string) (string, error) {
	if !p.enabled {
		return "", fmt.Errorf("claude plugin is disabled")
	}

	prompt := fmt.Sprintf("Please generate code based on these requirements:\n\n%s", requirements)
	return p.SendPrompt(prompt)
}

func init() {
	plugins.RegisterPluginFactory("claude-cli", NewClaudePlugin)
}