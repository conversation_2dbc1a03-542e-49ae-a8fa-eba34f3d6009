package claudecli

import (
	"os"
	"strconv"
	"time"
)

type Config struct {
	Enabled         bool          `json:"enabled"`
	Timeout         time.Duration `json:"timeout"`
	InteractiveMode bool          `json:"interactive"`
	MaxRetries      int           `json:"max_retries"`
	RetryDelay      time.Duration `json:"retry_delay"`
}

func LoadConfig() *Config {
	config := &Config{
		Enabled:         true,
		Timeout:         30 * time.Second,
		InteractiveMode: false,
		MaxRetries:      3,
		RetryDelay:      1 * time.Second,
	}

	if enabled := os.Getenv("PLUGIN_CLAUDE_CLI_ENABLED"); enabled != "" {
		config.Enabled = enabled == "true"
	}

	if timeout := os.Getenv("PLUGIN_CLAUDE_CLI_TIMEOUT"); timeout != "" {
		if seconds, err := strconv.Atoi(timeout); err == nil {
			config.Timeout = time.Duration(seconds) * time.Second
		}
	}

	if interactive := os.Getenv("PLUGIN_CLAUDE_CLI_INTERACTIVE"); interactive != "" {
		config.InteractiveMode = interactive == "true"
	}

	if maxRetries := os.Getenv("PLUGIN_CLAUDE_CLI_MAX_RETRIES"); maxRetries != "" {
		if retries, err := strconv.Atoi(maxRetries); err == nil {
			config.MaxRetries = retries
		}
	}

	if retryDelay := os.Getenv("PLUGIN_CLAUDE_CLI_RETRY_DELAY"); retryDelay != "" {
		if seconds, err := strconv.Atoi(retryDelay); err == nil {
			config.RetryDelay = time.Duration(seconds) * time.Second
		}
	}

	return config
}

func (c *Config) ToMap() map[string]any {
	return map[string]any{
		"enabled":         c.Enabled,
		"timeout":         int(c.Timeout.Seconds()),
		"interactive":     c.InteractiveMode,
		"max_retries":     c.MaxRetries,
		"retry_delay":     int(c.RetryDelay.Seconds()),
	}
}