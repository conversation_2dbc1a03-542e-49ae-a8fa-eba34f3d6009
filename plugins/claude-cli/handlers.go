package claudecli

import (
	"context"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

type ClaudeHandlers struct {
	plugin *ClaudePlugin
}

func NewClaudeHandlers(plugin *ClaudePlugin) *ClaudeHandlers {
	return &ClaudeHandlers{
		plugin: plugin,
	}
}

type ChatRequest struct {
	Prompt string `json:"prompt" binding:"required"`
}

type ChatResponse struct {
	Success  bool   `json:"success"`
	Response string `json:"response,omitempty"`
	Error    string `json:"error,omitempty"`
}

type CodeReviewRequest struct {
	Code string `json:"code" binding:"required"`
}

type CodeGenerationRequest struct {
	Requirements string `json:"requirements" binding:"required"`
}

type FileAnalysisRequest struct {
	FilePath string `json:"file_path" binding:"required"`
}

func (h *ClaudeHand<PERSON>) ChatHandler(c *gin.Context) {
	var req ChatRequest
	if err := c.<PERSON><PERSON><PERSON>(&req); err != nil {
		c.<PERSON>N(http.StatusBadRequest, ChatResponse{
			Success: false,
			Error:   "Invalid request: " + err.Error(),
		})
		return
	}

	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	done := make(chan struct {
		response string
		err      error
	}, 1)

	go func() {
		response, err := h.plugin.SendPrompt(req.Prompt)
		done <- struct {
			response string
			err      error
		}{response, err}
	}()

	select {
	case result := <-done:
		if result.err != nil {
			c.JSON(http.StatusInternalServerError, ChatResponse{
				Success: false,
				Error:   result.err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, ChatResponse{
			Success:  true,
			Response: result.response,
		})

	case <-ctx.Done():
		c.JSON(http.StatusRequestTimeout, ChatResponse{
			Success: false,
			Error:   "Request timeout",
		})
	}
}

func (h *ClaudeHandlers) CodeReviewHandler(c *gin.Context) {
	var req CodeReviewRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ChatResponse{
			Success: false,
			Error:   "Invalid request: " + err.Error(),
		})
		return
	}

	response, err := h.plugin.CodeReview(req.Code)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ChatResponse{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, ChatResponse{
		Success:  true,
		Response: response,
	})
}

func (h *ClaudeHandlers) CodeGenerationHandler(c *gin.Context) {
	var req CodeGenerationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ChatResponse{
			Success: false,
			Error:   "Invalid request: " + err.Error(),
		})
		return
	}

	response, err := h.plugin.GenerateCode(req.Requirements)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ChatResponse{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, ChatResponse{
		Success:  true,
		Response: response,
	})
}

func (h *ClaudeHandlers) FileAnalysisHandler(c *gin.Context) {
	var req FileAnalysisRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ChatResponse{
			Success: false,
			Error:   "Invalid request: " + err.Error(),
		})
		return
	}

	response, err := h.plugin.ProcessFile(req.FilePath)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ChatResponse{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, ChatResponse{
		Success:  true,
		Response: response,
	})
}

func (h *ClaudeHandlers) StatusHandler(c *gin.Context) {
	status := map[string]any{
		"plugin_name": h.plugin.Name(),
		"enabled":     h.plugin.enabled,
		"timeout":     h.plugin.timeout.String(),
		"interactive": h.plugin.interactiveMode,
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"status":  status,
	})
}

func (h *ClaudeHandlers) RegisterRoutes(router *gin.RouterGroup) {
	claude := router.Group("/claude")
	{
		claude.POST("/chat", h.ChatHandler)
		claude.POST("/code-review", h.CodeReviewHandler)
		claude.POST("/code-generation", h.CodeGenerationHandler)
		claude.POST("/file-analysis", h.FileAnalysisHandler)
		claude.GET("/status", h.StatusHandler)
	}
}