<PERSON><PERSON>, <PERSON> rất phù hợp để tích hợp vớ<PERSON>! Dưới đây là một số cách:

## 1. <PERSON><PERSON> dụng os/exec để gọi <PERSON>I

```go
package main

import (
    "fmt"
    "os/exec"
    "strings"
)

func callClaude(prompt string) (string, error) {
    cmd := exec.Command("claude", prompt)
    output, err := cmd.Output()
    if err != nil {
        return "", err
    }
    return strings.TrimSpace(string(output)), nil
}

func main() {
    response, err := callClaude("Hello, how are you?")
    if err != nil {
        fmt.Printf("Error: %v\n", err)
        return
    }
    fmt.Println(response)
}
```

## 2. Tích hợp với Cobra CLI

```go
package main

import (
    "fmt"
    "os/exec"
    "github.com/spf13/cobra"
)

var rootCmd = &cobra.Command{
    Use:   "my-ai-tool",
    Short: "AI tool wrapper",
}

var claudeCmd = &cobra.Command{
    Use:   "claude [prompt]",
    Short: "Chat with <PERSON>",
    Args:  cobra.MinimumNArgs(1),
    Run: func(cmd *cobra.Command, args []string) {
        prompt := strings.Join(args, " ")
        output, err := exec.Command("claude", prompt).Output()
        if err != nil {
            fmt.Printf("Error: %v\n", err)
            return
        }
        fmt.Print(string(output))
    },
}

func init() {
    rootCmd.AddCommand(claudeCmd)
}

func main() {
    rootCmd.Execute()
}
```

## 3. Tạo wrapper với nhiều tính năng

```go
package main

import (
    "bufio"
    "fmt"
    "os"
    "os/exec"
    "strings"
)

type AIWrapper struct {
    model string
}

func NewAIWrapper(model string) *AIWrapper {
    return &AIWrapper{model: model}
}

func (ai *AIWrapper) Chat(prompt string) error {
    var cmd *exec.Cmd
    
    switch ai.model {
    case "claude":
        cmd = exec.Command("claude", prompt)
    case "gpt":
        cmd = exec.Command("gpt", prompt)
    default:
        return fmt.Errorf("unsupported model: %s", ai.model)
    }
    
    cmd.Stdout = os.Stdout
    cmd.Stderr = os.Stderr
    return cmd.Run()
}

func (ai *AIWrapper) Interactive() {
    scanner := bufio.NewScanner(os.Stdin)
    fmt.Printf("Chat with %s (type 'exit' to quit):\n", ai.model)
    
    for {
        fmt.Print("> ")
        scanner.Scan()
        input := scanner.Text()
        
        if input == "exit" {
            break
        }
        
        if err := ai.Chat(input); err != nil {
            fmt.Printf("Error: %v\n", err)
        }
    }
}

func main() {
    ai := NewAIWrapper("claude")
    ai.Interactive()
}
```

## 4. Sử dụng Anthropic API trực tiếp

```go
package main

import (
    "bytes"
    "encoding/json"
    "fmt"
    "io"
    "net/http"
)

type AnthropicClient struct {
    apiKey string
    baseURL string
}

type Message struct {
    Role    string `json:"role"`
    Content string `json:"content"`
}

type Request struct {
    Model     string    `json:"model"`
    MaxTokens int       `json:"max_tokens"`
    Messages  []Message `json:"messages"`
}

func NewAnthropicClient(apiKey string) *AnthropicClient {
    return &AnthropicClient{
        apiKey:  apiKey,
        baseURL: "https://api.anthropic.com/v1",
    }
}

func (c *AnthropicClient) SendMessage(prompt string) (string, error) {
    req := Request{
        Model:     "claude-3-sonnet-20240229",
        MaxTokens: 1000,
        Messages: []Message{
            {Role: "user", Content: prompt},
        },
    }
    
    jsonData, err := json.Marshal(req)
    if err != nil {
        return "", err
    }
    
    httpReq, err := http.NewRequest("POST", c.baseURL+"/messages", bytes.NewBuffer(jsonData))
    if err != nil {
        return "", err
    }
    
    httpReq.Header.Set("Content-Type", "application/json")
    httpReq.Header.Set("x-api-key", c.apiKey)
    httpReq.Header.Set("anthropic-version", "2023-06-01")
    
    client := &http.Client{}
    resp, err := client.Do(httpReq)
    if err != nil {
        return "", err
    }
    defer resp.Body.Close()
    
    body, err := io.ReadAll(resp.Body)
    if err != nil {
        return "", err
    }
    
    // Parse response (simplified)
    return string(body), nil
}
```

## 5. Build và sử dụng

```bash
# Tạo go.mod
go mod init my-ai-tool

# Nếu dùng Cobra
go get github.com/spf13/cobra

# Build
go build -o my-ai-tool

# Chạy
./my-ai-tool claude "Hello world"
```

Bạn muốn tập trung vào phương pháp nào? Tôi có thể hướng dẫn chi tiết hơn.