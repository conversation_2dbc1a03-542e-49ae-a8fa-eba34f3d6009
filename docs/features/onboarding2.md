Chắc chắn rồi. Dựa trên mã nguồn Go bạn đã cung cấp và bản phác thảo API ban đầu, tôi đã hoàn thiện tài liệu thiết kế API cho quy trình Onboarding.

Tài liệu này được thiết kế để nhất quán với các quy tắc và cấu trúc hiện có trong codebase của bạn, bao gồm việ<PERSON> sử dụng DTO, định dạng phản hồ<PERSON> chu<PERSON>, và kiến trúc hướng module.

---

## **API Design: User Onboarding Flow**

### **1. Overview**

Quy trình Onboarding được thiết kế để hướng dẫn người dùng mới qua các bước thiết lập ban đầu một cách tuần tự, bao gồ<PERSON> xá<PERSON> thực email, tạo tenant, thiết lập website đầu tiên và hoàn thiện hồ sơ. API cung cấp các endpoints đ<PERSON> theo dõi, b<PERSON><PERSON> đầu, ho<PERSON>n thành và bỏ qua từng bước trong quy trình.

### **2. Authentication**

Tất cả các endpoint trong tài liệu này đều được bảo vệ và yêu cầu một `Bearer Token` (JWT) hợp lệ trong header `Authorization`. ID của người dùng sẽ được trích xuất từ token.

### **3. Data Models (DTOs)**

Các DTO chính được sử dụng trong các endpoints này, phản ánh cấu trúc trong `modules/auth/dto/onboarding.go` và các file liên quan.

#### `OnboardingProgressDetail`

```json
{
  "step_name": "tenant_setup",
  "status": "in_progress" | "completed" | "skipped" | "not_started",
  "started_at": "2023-10-27T10:00:00Z",
  "completed_at": null,
  "data": { "tenant_name_draft": "Công ty ABC" }
}
```

#### `OnboardingStatusResponse`

```json
{
  "onboarding_status": "in_progress",
  "onboarding_step": "website_setup",
  "progress": [
    {
      "step_name": "email_verification",
      "status": "completed",
      "started_at": "2023-10-27T09:55:00Z",
      "completed_at": "2023-10-27T09:58:00Z",
      "data": null
    },
    {
      "step_name": "tenant_setup",
      "status": "completed",
      "started_at": "2023-10-27T10:00:00Z",
      "completed_at": "2023-10-27T10:05:00Z",
      "data": { "tenant_id": 123 }
    },
    {
      "step_name": "website_setup",
      "status": "in_progress",
      "started_at": "2023-10-27T10:05:01Z",
      "completed_at": null,
      "data": null
    }
  ],
  "completed_steps": 2,
  "total_steps": 5,
  "completion_rate": 40.0
}
```

#### `OnboardingNextStepResponse`

```json
{
  "next_step": "website_setup",
  "is_completed": false,
  "message": "Next step: website_setup"
}
```

### **4. API Endpoints**

#### 4.1. **Initialize Onboarding**

Endpoint này khởi tạo các bản ghi tiến trình cho tất cả các bước onboarding của một người dùng mới. Nên được gọi một lần ngay sau khi người dùng đăng ký thành công.

*   **Endpoint:** `POST /api/admin/v1/onboarding/initialize`
*   **Description:** Initializes the onboarding progress for the authenticated user.
*   **Request Body:** None.
*   **Success Response:** `200 OK`
    *   **Body:** `OnboardingStatusResponse` (trạng thái ban đầu).
*   **Error Responses:**
    *   `401 Unauthorized`: JWT token không hợp lệ hoặc thiếu.
    *   `409 Conflict`: `{"error_code": "ONBOARDING_ALREADY_INITIALIZED", "message": "Onboarding progress has already been initialized for this user."}`

---

#### 4.2. **Get Onboarding Status**

Endpoint này lấy trạng thái onboarding hiện tại của người dùng, bao gồm tiến trình chi tiết của từng bước.

*   **Endpoint:** `GET /api/admin/v1/onboarding/status`
*   **Description:** Retrieves the current onboarding status for the authenticated user.
*   **Request Body:** None.
*   **Success Response:** `200 OK`
    *   **Body:** `OnboardingStatusResponse`.
*   **Error Responses:**
    *   `401 Unauthorized`: JWT token không hợp lệ hoặc thiếu.
    *   `404 Not Found`: `{"error_code": "ONBOARDING_NOT_INITIALIZED", "message": "Onboarding progress not found. Please initialize first."}`

---

#### 4.3. **Complete an Onboarding Step**

Endpoint này dùng để đánh dấu một bước trong quy trình onboarding đã hoàn thành và xử lý dữ liệu liên quan. Đây là endpoint chính để tiến hành onboarding.

*   **Endpoint:** `POST /api/admin/v1/onboarding/steps/complete`
*   **Description:** Marks an onboarding step as complete and processes associated data.
*   **Request Body:** `dto.CompleteOnboardingStepRequest`
    ```json
    {
      "step_name": "tenant_setup", // Required
      "data": {
        // Payload specific to the step.
        // For "tenant_setup", this would match CreateTenantOnboardingRequest
        "tenant_type": "company",
        "tenant_name": "Công ty TNHH WNAPI",
        "company_name": "Công ty TNHH WNAPI",
        "tax_code": "0123456789"
      }
    }
    ```
*   **Success Response:** `200 OK`
    *   **Body:** `OnboardingNextStepResponse`, hướng dẫn frontend bước tiếp theo.
*   **Error Responses:**
    *   `400 Bad Request`: `{"error_code": "VALIDATION_ERROR", "message": "Invalid data for step 'tenant_setup'.", "details": [{"field": "tenant_name", "message": "is required"}]}`
    *   `404 Not Found`: `{"error_code": "ONBOARDING_NOT_INITIALIZED", "message": "Onboarding progress not found."}`
    *   `409 Conflict`: `{"error_code": "STEP_ALREADY_COMPLETED", "message": "Step 'tenant_setup' has already been completed."}`
    *   `500 Internal Server Error`: `{"error_code": "STEP_COMPLETION_FAILED", "message": "Failed to process step completion."}`

---

#### 4.4. **Skip an Onboarding Step**

Bỏ qua một bước onboarding không bắt buộc.

*   **Endpoint:** `POST /api/admin/v1/onboarding/steps/skip`
*   **Description:** Skips an optional onboarding step.
*   **Request Body:** `dto.SkipOnboardingStepRequest`
    ```json
    {
      "step_name": "profile_setup", // Required
      "reason": "Will do it later"   // Optional
    }
    ```
*   **Success Response:** `200 OK`
    *   **Body:** `OnboardingNextStepResponse`.
*   **Error Responses:**
    *   `400 Bad Request`: `{"error_code": "STEP_CANNOT_BE_SKIPPED", "message": "Step 'tenant_setup' is mandatory and cannot be skipped."}`
    *   `409 Conflict`: `{"error_code": "STEP_ALREADY_COMPLETED", "message": "Step 'profile_setup' has already been completed."}`

---

#### 4.5. **Get Next Onboarding Step**

Một endpoint tiện ích để lấy nhanh bước tiếp theo mà không cần toàn bộ trạng thái.

*   **Endpoint:** `GET /api/admin/v1/onboarding/next-step`
*   **Description:** Retrieves the next recommended step for the user to complete.
*   **Request Body:** None.
*   **Success Response:** `200 OK`
    *   **Body:** `OnboardingNextStepResponse`.
*   **Error Responses:**
    *   `401 Unauthorized`: JWT token không hợp lệ hoặc thiếu.
    *   `404 Not Found`: `{"error_code": "ONBOARDING_NOT_INITIALIZED", "message": "Onboarding progress not found."}`

### **5. Helper Endpoints**

Các endpoints này cung cấp dữ liệu hỗ trợ cho các form trong quá trình onboarding.

#### **GET /api/admin/v1/onboarding/suggestions/tenant-name**
*   **Description:** Gợi ý tên tenant dựa trên tên đầy đủ của người dùng.
*   **Query Params:** `?full_name=John Doe`
*   **Success Response:** `200 OK`
    ```json
    {
        "data": {
            "suggestions": ["john-doe-company", "johndoe-inc", "doecorp"]
        }
    }
    ```

#### **GET /api/admin/v1/onboarding/templates**
*   **Description:** Lấy danh sách các mẫu website có sẵn.
*   **Success Response:** `200 OK`
    ```json
    {
        "data": [
            {
                "id": "ecommerce_basic",
                "name": "E-commerce Basic",
                "description": "A basic template for online stores.",
                "preview_url": "https://...",
                "category": "ecommerce"
            }
        ]
    }
    ```

### **6. Onboarding Flow Example**

1.  **Sau khi đăng ký thành công**, frontend gọi `POST /api/admin/v1/onboarding/initialize`.
2.  Frontend nhận về trạng thái ban đầu và redirect người dùng đến trang onboarding.
3.  Frontend gọi `GET /api/admin/v1/onboarding/status` để hiển thị một checklist các bước.
4.  Người dùng điền thông tin cho bước `tenant_setup`.
5.  Frontend gọi `POST /api/admin/v1/onboarding/steps/complete` với `step_name: "tenant_setup"` và dữ liệu của tenant.
6.  Backend xử lý, tạo tenant, cập nhật trạng thái của bước thành `completed`.
7.  Backend trả về `OnboardingNextStepResponse` chứa `next_step: "website_setup"`.
8.  Frontend redirect người dùng đến trang thiết lập website.
9.  Quy trình lặp lại cho đến khi `is_completed` trong `OnboardingNextStepResponse` là `true`.
10. Frontend redirect người dùng đến trang Dashboard chính.

---

### **Tóm tắt bằng Tiếng Việt**

Tài liệu này định nghĩa các API cho quy trình giới thiệu và thiết lập ban đầu (onboarding) cho người dùng mới.

-   **Luồng chính:**
    1.  **Khởi tạo (`/initialize`):** Bắt đầu quy trình onboarding sau khi đăng ký.
    2.  **Kiểm tra trạng thái (`/status`):** Lấy danh sách các bước và tiến độ hiện tại.
    3.  **Hoàn thành bước (`/steps/complete`):** Gửi dữ liệu để hoàn thành một bước cụ thể (ví dụ: tạo tenant, tạo website). API sẽ trả về bước tiếp theo.
    4.  **Bỏ qua bước (`/steps/skip`):** Cho phép bỏ qua các bước không bắt buộc.
-   **Endpoint tiện ích:** Cung cấp các API để gợi ý tên, lấy danh sách mẫu website, giúp trải nghiệm người dùng mượt mà hơn.
-   **Cấu trúc:** Toàn bộ API được thiết kế để phù hợp với kiến trúc module, các DTO và mô hình dữ liệu đã có trong codebase, đảm bảo tính nhất quán và dễ dàng mở rộng.